import { cn } from "@/lib/utils";
import { Package, Sword, Shield, Gem, Archive, Scroll } from "lucide-react";

export type CraftingCategory = "all" | "weapons" | "armor" | "consumables" | "misc" | "quest";

interface CraftingCategoryTabsProps {
    activeCategory: CraftingCategory;
    onCategoryChange: (category: CraftingCategory) => void;
    children: React.ReactNode;
}

type CategoryFilter = {
    icon: React.ElementType;
    color: string;
};

export default function CraftingCategoryTabs({
    activeCategory,
    onCategoryChange,
    children,
}: CraftingCategoryTabsProps) {
    const categoryFilters: Record<CraftingCategory, CategoryFilter> = {
        all: { icon: Package, color: "text-primary" },
        weapons: { icon: Sword, color: "text-error" },
        armor: { icon: Shield, color: "text-info" },
        consumables: { icon: Gem, color: "text-success" },
        misc: { icon: Archive, color: "text-warning" },
        quest: { icon: Scroll, color: "text-secondary" },
    };

    const categoryNames: Record<CraftingCategory, string> = {
        all: "All",
        weapons: "Weapons",
        armor: "Armor",
        consumables: "Consumables",
        misc: "Misc",
        quest: "Quest",
    };

    const tabs = Object.keys(categoryFilters) as CraftingCategory[];

    return (
        <div className="px-2 md:px-0 space-y-4">
            {/* Tab Navigation */}
            <div className="tabs tabs-box bg-base-200 p-1">
                {tabs.map((tab) => {
                    const { icon: Icon, color } = categoryFilters[tab];
                    return (
                        <button
                            key={tab}
                            role="tab"
                            title={categoryNames[tab]}
                            className={cn(
                                "tab transition-all",
                                // Mobile: flex-1 for full width, py-2 for padding
                                "flex-1 py-2 md:flex-initial md:gap-2",
                                activeCategory === tab ? "tab-active bg-base-300" : "hover:bg-base-300/50"
                            )}
                            onClick={() => onCategoryChange(tab)}
                        >
                            <Icon className={cn("w-4 h-4", activeCategory === tab ? color : "opacity-50")} />
                            {/* Show text label only on desktop */}
                            <span className={cn("hidden md:inline", activeCategory === tab && "font-semibold")}>
                                {categoryNames[tab]}
                            </span>
                        </button>
                    );
                })}
            </div>

            {/* Tab Content */}
            <div className="tab-content bg-base-100 border border-base-300 rounded-box p-4 shadow-xl">{children}</div>
        </div>
    );
}
