import { DisplayItem } from "@/components/DisplayItem";
import { cn } from "@/lib/utils";
import { safeTimestampToNumber, hasTimestampExpired, getMillisecondsUntilTimestamp } from "@/utils/timestampHelpers";
import { ChevronDown, Clock, Hammer, RefreshCw, XCircle, Package2, Sparkles } from "lucide-react";
import { useState } from "react";
import useCancelCraft from "../api/useCancelCraft";
import useCollectCraft from "../api/useCollectCraft";
import useStartCraft from "../api/useStartCraft";
import type { CraftingQueueItem } from "../types/crafting";

export default function CraftingQueue({
    craftingQueue,
    maxCraftQueue,
    isLoading,
    isError,
}: {
    craftingQueue: CraftingQueueItem[];
    maxCraftQueue: number;
    isLoading: boolean;
    isError: boolean;
}) {
    const [showQueue, setShowQueue] = useState(true);

    // Use the collect craft hook
    const { mutate: collectCraft, isPending: isCollecting } = useCollectCraft();

    // Use the start craft hook
    const { mutate: startCraft, isPending: isStarting } = useStartCraft();

    // Use the cancel craft hook
    const { mutate: cancelCraft, isPending: isCancelling } = useCancelCraft();

    // Check if a crafting item is completed
    const isCompleted = (item: CraftingQueueItem) => {
        return hasTimestampExpired(item?.endsAt);
    };

    // Format time remaining
    const formatTimeRemaining = (endsAt: string | bigint | number | undefined | null) => {
        const timeLeftMs = getMillisecondsUntilTimestamp(endsAt);

        if (timeLeftMs === -1) return "Unknown";
        if (timeLeftMs <= 0) return "Ready";

        const hours = Math.floor(timeLeftMs / (1000 * 60 * 60));
        const minutes = Math.floor((timeLeftMs % (1000 * 60 * 60)) / (1000 * 60));
        const seconds = Math.floor((timeLeftMs % (1000 * 60)) / 1000);

        if (hours > 0) {
            return `${hours}h ${minutes}m`;
        } else if (minutes > 0) {
            return `${minutes}m ${seconds}s`;
        } else {
            return `${seconds}s`;
        }
    };

    // Calculate progress percentage
    const getProgressPercentage = (item: CraftingQueueItem) => {
        const startTime = safeTimestampToNumber(item?.startedAt);
        const endTime = safeTimestampToNumber(item?.endsAt);

        if (startTime === 0 || endTime === 0) return 0;

        const now = Date.now();
        const totalTime = endTime - startTime;
        const elapsed = now - startTime;

        if (totalTime <= 0) return 100;
        return Math.min(100, Math.max(0, (elapsed / totalTime) * 100));
    };

    // Collect and recraft function
    const handleCollectAndRecraft = (item: CraftingQueueItem) => {
        const recipeId = item?.crafting_recipe.id;
        const itemId = item?.id;

        if (!recipeId || !itemId) {
            console.error("Missing recipe ID or item ID for collect and recraft");
            return;
        }

        collectCraft(
            { id: itemId },
            {
                onSuccess: () => {
                    // Start crafting the same recipe again
                    startCraft({ recipeId, amount: 1 });
                },
            }
        );
    };

    const hasReadyItems = craftingQueue.some((item) => isCompleted(item));

    return (
        <div className="card-body p-0">
            {/* Header */}
            <div className="collapse collapse-arrow bg-base-100">
                <input type="checkbox" checked={showQueue} onChange={(e) => setShowQueue(e.target.checked)} />
                <div className="collapse-title flex items-center justify-between p-4">
                    <div className="flex items-center gap-3">
                        <div className="p-2 bg-primary/20 rounded-lg">
                            <Package2 className="size-5 text-primary" />
                        </div>
                        <div>
                            <h3 className="font-display text-base font-semibold">Crafting Queue</h3>
                            {!isLoading && !isError && (
                                <div className="flex items-center gap-2 mt-1">
                                    <progress
                                        className="progress progress-primary w-20 h-2"
                                        value={craftingQueue.length}
                                        max={maxCraftQueue}
                                    ></progress>
                                    <span className="text-xs text-base-content/60">
                                        {craftingQueue.length}/{maxCraftQueue}
                                    </span>
                                </div>
                            )}
                        </div>
                    </div>
                    <div className="flex items-center gap-2">
                        {hasReadyItems && (
                            <div className="badge badge-success badge-sm gap-1 animate-pulse">
                                <Sparkles className="size-3" />
                                Ready!
                            </div>
                        )}
                    </div>
                </div>

                <div className="collapse-content px-4 pb-4">
                    {isLoading && (
                        <div className="flex items-center justify-center py-8">
                            <span className="loading loading-spinner loading-md text-primary"></span>
                        </div>
                    )}

                    {isError && (
                        <div className="alert alert-error">
                            <XCircle className="size-5" />
                            <span>Failed to load crafting queue</span>
                        </div>
                    )}

                    {!isLoading && !isError && craftingQueue.length > 0 ? (
                        <div className="space-y-3">
                            {craftingQueue.map((item) => {
                                const completed = isCompleted(item);
                                const outputItem = item?.crafting_recipe.outputs[0];
                                const progress = getProgressPercentage(item);

                                return (
                                    <div
                                        key={item?.id}
                                        className={cn(
                                            "card bg-base-200 border-2 transition-all",
                                            completed ? "border-success shadow-lg shadow-success/20" : "border-base-300"
                                        )}
                                    >
                                        <div className="card-body p-3">
                                            <div className="flex items-center gap-3">
                                                <div className="indicator">
                                                    {outputItem?.amount && outputItem.amount > 1 && (
                                                        <span className="indicator-item badge badge-secondary badge-xs">
                                                            x{outputItem.amount}
                                                        </span>
                                                    )}
                                                    <div className="avatar">
                                                        <div className="w-12 rounded-lg bg-base-300 p-1">
                                                            {outputItem?.image ? (
                                                                <DisplayItem item={outputItem} className="" />
                                                            ) : (
                                                                <div className="text-2xl flex items-center justify-center h-full">
                                                                    📦
                                                                </div>
                                                            )}
                                                        </div>
                                                    </div>
                                                </div>
                                                <div className="flex-1">
                                                    <h4 className="font-medium text-sm">
                                                        {outputItem?.name || "Unknown Item"}
                                                    </h4>
                                                    <div className="flex items-center gap-2 mt-1">
                                                        {completed ? (
                                                            <div className="badge badge-success badge-sm gap-1">
                                                                <Sparkles className="size-3" />
                                                                Ready to collect!
                                                            </div>
                                                        ) : (
                                                            <>
                                                                <Clock className="size-3 text-info" />
                                                                <span className="text-xs text-base-content/70">
                                                                    {formatTimeRemaining(item?.endsAt)}
                                                                </span>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>

                                            {/* Progress Bar */}
                                            {!completed && (
                                                <div className="mt-2">
                                                    <progress
                                                        className="progress progress-info h-2"
                                                        value={progress}
                                                        max="100"
                                                    ></progress>
                                                </div>
                                            )}

                                            {/* Actions */}
                                            <div className="mt-3">
                                                {completed ? (
                                                    <div className="flex gap-2">
                                                        <button
                                                            disabled={isCollecting || isStarting}
                                                            className={cn(
                                                                "btn btn-success btn-sm flex-1",
                                                                (isCollecting || isStarting) && "loading"
                                                            )}
                                                            onClick={() => {
                                                                if (item?.id) {
                                                                    collectCraft({ id: item.id });
                                                                }
                                                            }}
                                                        >
                                                            {!isCollecting && <Package2 className="size-4" />}
                                                            Collect
                                                        </button>
                                                        <button
                                                            disabled={isCollecting || isStarting}
                                                            className={cn(
                                                                "btn btn-info btn-sm",
                                                                (isCollecting || isStarting) && "loading"
                                                            )}
                                                            onClick={() => handleCollectAndRecraft(item)}
                                                        >
                                                            {!(isCollecting || isStarting) && (
                                                                <RefreshCw className="size-4" />
                                                            )}
                                                        </button>
                                                    </div>
                                                ) : (
                                                    <button
                                                        disabled={isCancelling}
                                                        className={cn(
                                                            "btn btn-error btn-outline btn-sm btn-block",
                                                            isCancelling && "loading"
                                                        )}
                                                        onClick={() => {
                                                            if (item?.id) {
                                                                cancelCraft({ id: item.id });
                                                            }
                                                        }}
                                                    >
                                                        {!isCancelling && <XCircle className="-ml-2 size-4" />}
                                                        Cancel
                                                    </button>
                                                )}
                                            </div>
                                        </div>
                                    </div>
                                );
                            })}
                        </div>
                    ) : !isLoading && !isError ? (
                        <div className="flex flex-col items-center justify-center py-8">
                            <div className="p-4 bg-base-300/50 rounded-full mb-3">
                                <Hammer className="size-8 text-base-content/50" />
                            </div>
                            <p className="text-base-content/70 font-display">No items in queue</p>
                            <p className="text-sm text-base-content/50 mt-1">Start crafting to see items here</p>
                        </div>
                    ) : null}
                </div>
            </div>
        </div>
    );
}
