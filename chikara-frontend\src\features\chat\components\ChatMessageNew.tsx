import { DisplayAvatar } from "@/components/DisplayAvatar";
import { ChatDropdownMenu } from "@/components/Menu/DropdownMenu";
import { cn } from "@/lib/utils";
import { User } from "@/types/user";
import { UTCDateMini } from "@date-fns/utc";
import { format } from "date-fns";
import { Link } from "react-router-dom";
import RenderChatText from "./RenderChatText";
import { formatTimeToNow } from "@/helpers/dateHelpers";
import type { ChatMessage } from "../types/chat";

interface ChatMessageProps {
    msg: ChatMessage;
    currentUser: User;
    setSelectedReplyMessage: (_message: ChatMessage) => void;
    setFocusChatMsgInput: (_focusChatMsgInput: boolean) => void;
}

export default function ChatMessage({
    msg,
    currentUser,
    setSelectedReplyMessage,
    setFocusChatMsgInput,
}: ChatMessageProps) {
    if (typeof msg.message !== "string") return null;

    const currentUserType = currentUser?.userType;
    const currentUserId = currentUser?.id;

    const renderUserBadge = (userType: string) => {
        if (userType === "admin") {
            return (
                <span className="badge badge-error badge-sm absolute -bottom-3 left-1/2 -translate-x-1/2 shadow-md">
                    Staff
                </span>
            );
        }
        if (userType === "prefect") {
            return (
                <span className="badge badge-secondary badge-sm absolute -bottom-3 left-1/2 -translate-x-1/2 shadow-md">
                    Prefect
                </span>
            );
        }
    };

    const includesMention = () => {
        if (msg.userId === currentUserId) return false;
        const parentMessageUserId = msg?.parentMessage?.userId || null;

        if (parentMessageUserId && parentMessageUserId === currentUserId) return true;
        return false;
    };

    const includesSlashMe = () => {
        if (msg?.message?.startsWith("/me")) return true;
        return false;
    };

    const scrollToMessage = (messageId: number) => {
        const element = document.getElementById(`message-${messageId}`);
        if (element) {
            element.scrollIntoView({ behavior: "smooth", block: "center" });
        }
    };

    return (
        <div
            id={`message-${msg.id}`}
            className={cn(
                "card relative mt-2 min-h-[80px] overflow-hidden pt-0.5 pr-2 pb-2 pl-20 shadow-lg transition-all hover:shadow-xl",
                msg?.parentMessageId ? "max-h-[240px]" : "max-h-[180px]",
                msg?.user?.userType === "admin"
                    ? "border-2 border-error/50 bg-gradient-to-br from-base-200 to-error/10"
                    : "border-2 border-primary/20 bg-base-200",
                includesMention() ? "ring-2 ring-accent ring-offset-2 ring-offset-base-100" : ""
            )}
        >
            <Link className="avatar absolute left-2 top-2" to={"/profile/" + msg.userId}>
                <div className="relative">
                    <div className="mask mask-squircle ring ring-primary ring-offset-2 ring-offset-base-100 size-14 2xl:size-16">
                        <DisplayAvatar src={msg.user} className="size-14 2xl:size-16" />
                    </div>
                    {renderUserBadge(msg?.user?.userType)}
                    <div className="badge badge-primary badge-sm absolute -left-1 -top-1 font-bold">
                        {msg?.user?.level}
                    </div>
                </div>
            </Link>

            <div className="flex flex-row">
                <p
                    className={cn(
                        includesSlashMe() && "hidden",
                        "truncate font-lili text-base-content text-sm md:text-[0.82rem] font-bold"
                    )}
                >
                    {msg?.user?.username}
                    {msg?.user?.id === 5 ? (
                        <span className="badge badge-accent badge-xs ml-1.5 animate-pulse">AI</span>
                    ) : null}
                </p>
                <small
                    data-tooltip-id="date-tooltip"
                    data-tooltip-content={format(new UTCDateMini(msg.createdAt), "PP, p")}
                    className="mt-0.5 mr-2 ml-auto cursor-pointer text-base-content/60 text-xs"
                >
                    {formatTimeToNow(msg.createdAt) ? `${formatTimeToNow(msg.createdAt)} ago` : "-"}
                </small>

                <ChatDropdownMenu
                    userType={currentUserType}
                    msg={msg}
                    setSelectedReplyMessage={setSelectedReplyMessage}
                    setFocusChatMsgInput={setFocusChatMsgInput}
                />
            </div>
            {msg && msg.parentMessageId && msg.parentMessageId > 0 && (
                <div
                    className="alert alert-info alert-sm mt-0.5 mb-1.5 cursor-pointer p-2 border-l-4 border-l-accent"
                    onClick={() => scrollToMessage(msg.parentMessageId as number)}
                >
                    <div className="flex items-center gap-2">
                        <div className="avatar">
                            <div className="mask mask-circle size-6">
                                <DisplayAvatar src={msg?.parentMessage?.user} />
                            </div>
                        </div>
                        <div className="flex-1 truncate">
                            <RenderChatText
                                className="text-info-content text-sm truncate"
                                msg={msg?.parentMessage}
                                imgClassName="max-h-5"
                            />
                        </div>
                    </div>
                </div>
            )}
            {msg.hidden ? (
                <p className="text-base-content/40 italic text-sm">--message removed--</p>
            ) : (
                <RenderChatText
                    className="text-base-content text-sm md:text-[0.8rem] md:leading-[1.2rem] break-words"
                    msg={msg}
                />
            )}
        </div>
    );
}
