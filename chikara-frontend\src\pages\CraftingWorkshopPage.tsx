import useGetCraftingQueue from "@/features/crafting/api/useGetCraftingQueue";
import useGetCraftingRecipes from "@/features/crafting/api/useGetCraftingRecipes";
import CraftableItem from "@/features/crafting/components/CraftableItem";
import CraftingCategoryTabs, { type CraftingCategory } from "@/features/crafting/components/CraftingCategoryTabs";
import CraftingQueue from "@/features/crafting/components/CraftingQueue";
import ItemUpgradeModal from "@/features/crafting/components/Upgrades/ItemUpgradeModal";
import useGetInventory from "@/hooks/api/useGetInventory";
import useIsTalentUnlocked from "@/hooks/api/useIsTalentUnlocked";
import { ItemTypes } from "@/types/item";
import { AlertCircle, Filter, Search, Sparkles, Hammer } from "lucide-react";
import { useMemo, useState } from "react";

export default function CraftingPanel() {
    const { isLoading, error, data: recipes } = useGetCraftingRecipes();
    const { data: userInventory, isLoading: isInventoryLoading } = useGetInventory();
    const {
        data: craftingQueue = [],
        isLoading: isCraftingQueueLoading,
        isError: isCraftingQueueError,
    } = useGetCraftingQueue();
    const [activeCategory, setActiveCategory] = useState<CraftingCategory>("all");
    const [searchQuery, setSearchQuery] = useState("");

    const multitaskerTalent = useIsTalentUnlocked("multitasker");
    const maxCraftQueue = 1 + (multitaskerTalent?.level || 0);

    const isCraftQueueFull = craftingQueue.length >= maxCraftQueue;

    // Map ItemTypes to our UI categories
    const mapItemTypeToCategory = (itemType: string): CraftingCategory => {
        if ([ItemTypes.weapon, ItemTypes.ranged, ItemTypes.offhand].includes(itemType as ItemTypes)) {
            return "weapons";
        }
        if (
            [
                ItemTypes.head,
                ItemTypes.chest,
                ItemTypes.hands,
                ItemTypes.legs,
                ItemTypes.feet,
                ItemTypes.shield,
            ].includes(itemType as ItemTypes)
        ) {
            return "armor";
        }
        if (itemType === ItemTypes.consumable) {
            return "consumables";
        }
        if (itemType === ItemTypes.quest) {
            return "quest";
        }
        // All other item types go to misc
        return "misc";
    };

    // Filter recipes based on category and search query
    const filteredRecipes = useMemo(() => {
        if (!recipes) return [];

        return recipes.filter((recipe) => {
            // Need at least one output
            if (!recipe.outputs || recipe.outputs.length === 0) return false;

            // First output is typically the main item
            const mainOutput = recipe.outputs[0];
            const category = mapItemTypeToCategory(mainOutput.itemType);

            // Filter by category
            if (activeCategory !== "all" && category !== activeCategory) return false;

            // Filter by search query
            if (searchQuery && !mainOutput.name.toLowerCase().includes(searchQuery.toLowerCase())) return false;

            return true;
        });
    }, [recipes, activeCategory, searchQuery]);

    return (
        <div className="max-w-(--breakpoint-lg) mx-auto flex flex-col lg:flex-row gap-6 p-4">
            <div className="flex flex-col gap-4 lg:w-80">
                {/* Crafting Queue Card */}
                <div className="card bg-base-200 shadow-xl border-2 border-primary/20">
                    <CraftingQueue
                        craftingQueue={craftingQueue}
                        maxCraftQueue={maxCraftQueue}
                        isLoading={isCraftingQueueLoading}
                        isError={isCraftingQueueError}
                    />
                </div>

                {/* Upgrade Item Card */}
                <div className="card bg-gradient-to-br from-accent/20 to-secondary/20 border-2 border-accent/30 shadow-xl">
                    <div className="card-body p-4">
                        <h3 className="card-title text-lg font-display text-accent flex items-center gap-2">
                            <Sparkles className="size-5" />
                            Item Enhancement
                        </h3>
                        <p className="text-sm text-base-content/70">
                            Upgrade your items to unlock their true potential!
                        </p>
                        <ItemUpgradeModal />
                    </div>
                </div>
            </div>

            <div className="flex-1 min-w-0 space-y-4">
                <div className="relative form-control">
                    <Search className="absolute left-3 top-1/2 -translate-y-1/2 size-4 text-gray-500" />
                    <input
                        type="text"
                        placeholder="Search recipes..."
                        className="w-full bg-gray-800/50 border border-purple-900/30 rounded-lg py-2 pl-10 pr-4 text-sm text-white placeholder:text-gray-500 focus:outline-hidden focus:ring-1 focus:ring-purple-500"
                        value={searchQuery}
                        onChange={(e) => setSearchQuery(e.target.value)}
                    />
                </div>

                {/* Category Tabs */}
                <CraftingCategoryTabs activeCategory={activeCategory} onCategoryChange={setActiveCategory} />

                {/* Content Area */}
                <div className="card bg-base-200/50 shadow-xl border-2 border-base-300">
                    <div className="card-body p-4">
                        {/* Loading State */}
                        {(isLoading || isInventoryLoading) && (
                            <div className="flex flex-col items-center justify-center py-12">
                                <span className="loading loading-spinner loading-lg text-primary"></span>
                                <p className="mt-4 text-base-content/70 font-display">Loading recipes...</p>
                            </div>
                        )}

                        {/* Error State */}
                        {error && (
                            <div className="alert alert-error shadow-lg text-stroke-0">
                                <AlertCircle className="size-6" />
                                <span>Failed to load recipes. Please try again later.</span>
                            </div>
                        )}

                        {/* Craftable Items */}
                        {!isLoading && !isInventoryLoading && !error && (
                            <div className="grid grid-cols-1 gap-4">
                                {filteredRecipes.map((recipe) => {
                                    const outputItem = recipe.outputs[0];
                                    return (
                                        <CraftableItem
                                            key={recipe.id}
                                            recipe={recipe}
                                            outputItem={outputItem}
                                            userInventory={userInventory}
                                            isCraftQueueFull={isCraftQueueFull}
                                        />
                                    );
                                })}
                            </div>
                        )}

                        {!isLoading && !isInventoryLoading && !error && filteredRecipes.length === 0 && (
                            <div className="flex flex-col items-center justify-center py-12">
                                <div className="p-4 bg-base-300/50 rounded-full mb-4">
                                    <Filter className="size-12 text-base-content/50" />
                                </div>
                                <p className="text-lg font-display text-base-content/70">No recipes found</p>
                                <p className="text-sm text-base-content/50 mt-2">
                                    Try adjusting your search or filters
                                </p>
                            </div>
                        )}
                    </div>
                </div>
            </div>
        </div>
    );
}
